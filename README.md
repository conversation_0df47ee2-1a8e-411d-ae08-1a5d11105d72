# Stock Holding Leaderboard Application

A React-based stock holding leaderboard application that allows users to manage stocks, users, and track profit/loss in real-time.

## Features

### 📈 Stock Management
- Add stocks with auto-generated IDs
- Real-time price updates every second (±0-2% variance)
- View all stocks with current pricing

### 👥 User Management
- Add users with auto-generated IDs
- Simple user registration with name only

### 🏆 Leaderboard & Trading
- Real-time leaderboard showing top 10 users by profit/loss
- Buy/sell stocks for users
- Automatic profit/loss calculation: (Current Price - Buying Price) × Quantity
- Live statistics dashboard
- Transaction history tracking

## Technology Stack

- **Frontend**: React 18 with Vite
- **UI Library**: Ant Design
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **State Management**: React Context API + useReducer
- **Data Persistence**: Local Storage
- **Real-time Updates**: setInterval for price fluctuations

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd pd-assesment
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Application Structure

```
src/
├── components/
│   └── Layout.jsx          # Main layout with navigation
├── context/
│   └── AppContext.jsx      # Global state management
├── hooks/
│   └── useLocalStorage.js  # Custom hook for localStorage
├── pages/
│   ├── StockEntry.jsx      # Stock data entry page
│   ├── UserEntry.jsx       # User data entry page
│   └── Leaderboard.jsx     # Main leaderboard page
├── App.jsx                 # Main app component with routing
└── main.jsx               # Application entry point
```

## Usage Guide

### 1. Stock Entry Page (`/stocks`)
- Add new stocks with name and initial price
- View all stocks with real-time price updates
- Stock prices automatically fluctuate every second

### 2. User Entry Page (`/users`)
- Add new users to the system
- View all registered users

### 3. Leaderboard Page (`/leaderboard`)
- View current stock prices
- Execute buy/sell transactions for users
- See real-time leaderboard of top 10 performers
- Monitor overall statistics

## Key Features Explained

### Real-time Price Updates
Stock prices update every second with a random variance between -2% to +2%, simulating real market conditions.

### Profit/Loss Calculation
The system calculates profit/loss using the formula:
```
Profit = (Current Stock Price - Average Buy Price) × Quantity Held
```

### Transaction System
- **Buy**: Adds to user's holdings at current market price
- **Sell**: Removes from holdings and realizes profit/loss immediately

### Data Persistence
All data is stored in browser's localStorage, ensuring data persists between sessions.

## Time Complexity Analysis

- **Adding Stock/User**: O(1)
- **Price Updates**: O(n) where n = number of stocks
- **Profit Calculation**: O(m × h) where m = number of users, h = average holdings per user
- **Leaderboard Sorting**: O(m log m) where m = number of users

## Build for Production

```bash
npm run build
```

The built files will be in the `build/` directory.

## Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Code Quality
The project uses ESLint for code quality and follows React best practices.

## Browser Compatibility
- Modern browsers supporting ES2020+
- Chrome, Firefox, Safari, Edge (latest versions)

## License
This project is for assessment purposes.
