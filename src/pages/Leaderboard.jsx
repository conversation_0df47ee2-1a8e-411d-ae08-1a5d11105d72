import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Select, 
  InputNumber, 
  Button, 
  Table, 
  Typography, 
  Space,
  message,
  Tag,
  Avatar,
  Row,
  Col,
  Statistic,
  Radio
} from 'antd';
import { 
  TrophyOutlined, 
  UserOutlined, 
  StockOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import { useApp } from '../context/AppContext';

const { Title, Text } = Typography;
const { Option } = Select;

function Leaderboard() {
  const { state, addHolding, calculateUserProfits } = useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [userProfits, setUserProfits] = useState([]);

  // Update user profits whenever state changes
  useEffect(() => {
    const profits = calculateUserProfits();
    setUserProfits(profits);
  }, [state.stocks, state.holdings, state.users, calculateUserProfits]);

  const handleHoldingSubmit = async (values) => {
    setLoading(true);
    try {
      const { userId, stockId, quantity, action } = values;
      
      if (!userId || !stockId || !quantity || !action) {
        message.error('Please fill in all fields');
        return;
      }

      if (quantity <= 0) {
        message.error('Quantity must be greater than 0');
        return;
      }

      addHolding(userId, stockId, quantity, action);
      form.resetFields();
      message.success(`Successfully ${action === 'buy' ? 'bought' : 'sold'} ${quantity} shares!`);
    } catch (error) {
      message.error('Failed to process transaction');
      console.error('Error processing holding:', error);
    } finally {
      setLoading(false);
    }
  };

  const stockColumns = [
    {
      title: 'Stock ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (id) => <Tag color="blue">#{id}</Tag>,
    },
    {
      title: 'Stock Name',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <Space>
          <StockOutlined />
          <Text strong>{name}</Text>
        </Space>
      ),
    },
    {
      title: 'Current Price',
      dataIndex: 'price',
      key: 'price',
      width: 120,
      render: (price) => (
        <Text strong className="text-green-600">
          ${price.toFixed(2)}
        </Text>
      ),
      sorter: (a, b) => a.price - b.price,
    },
  ];

  const leaderboardColumns = [
    {
      title: 'Rank',
      key: 'rank',
      width: 80,
      render: (_, __, index) => {
        const rankColors = ['#FFD700', '#C0C0C0', '#CD7F32'];
        const color = rankColors[index] || '#1890ff';
        return (
          <Space>
            <Avatar 
              style={{ backgroundColor: color }}
              icon={<TrophyOutlined />}
            />
            <Text strong>#{index + 1}</Text>
          </Space>
        );
      },
    },
    {
      title: 'User Name',
      dataIndex: 'userName',
      key: 'userName',
      render: (name) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <Text strong>{name}</Text>
        </Space>
      ),
    },
    {
      title: 'Profit/Loss',
      dataIndex: 'totalProfit',
      key: 'totalProfit',
      width: 150,
      render: (profit) => {
        const isProfit = profit >= 0;
        return (
          <Space>
            {isProfit ? (
              <RiseOutlined className="text-green-500" />
            ) : (
              <FallOutlined className="text-red-500" />
            )}
            <Text 
              strong 
              className={isProfit ? 'text-green-600' : 'text-red-600'}
            >
              ${profit.toFixed(2)}
            </Text>
          </Space>
        );
      },
      sorter: (a, b) => b.totalProfit - a.totalProfit,
    },
  ];

  const totalUsers = state.users.length;
  const totalStocks = state.stocks.length;
  const totalTransactions = state.holdings.length;
  const topProfit = userProfits.length > 0 ? userProfits[0].totalProfit : 0;

  return (
    <div className="space-y-6">
      <div>
        <Title level={2}>Stock Holding Leaderboard</Title>
        <Text type="secondary">
          Real-time leaderboard showing top performers based on their stock holdings profit/loss.
        </Text>
      </div>

      {/* Statistics */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={totalUsers}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Stocks"
              value={totalStocks}
              prefix={<StockOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Transactions"
              value={totalTransactions}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Top Profit"
              value={topProfit}
              precision={2}
              prefix="$"
              valueStyle={{ color: topProfit >= 0 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Stock List */}
      <Card title="Current Stock Prices" className="shadow-sm">
        <Table
          columns={stockColumns}
          dataSource={state.stocks}
          rowKey="id"
          pagination={false}
          scroll={{ x: 600 }}
          locale={{
            emptyText: 'No stocks available. Add stocks first!',
          }}
        />
      </Card>

      {/* User Holding Management Form */}
      <Card title="User Holding Management" className="shadow-sm">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleHoldingSubmit}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                label="Select User"
                name="userId"
                rules={[{ required: true, message: 'Please select a user' }]}
              >
                <Select 
                  placeholder="Choose user"
                  size="large"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {state.users.map(user => (
                    <Option key={user.id} value={user.id}>
                      {user.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Select Stock"
                name="stockId"
                rules={[{ required: true, message: 'Please select a stock' }]}
              >
                <Select 
                  placeholder="Choose stock"
                  size="large"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {state.stocks.map(stock => (
                    <Option key={stock.id} value={stock.id}>
                      {stock.name} (${stock.price.toFixed(2)})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item
                label="Quantity"
                name="quantity"
                rules={[
                  { required: true, message: 'Please enter quantity' },
                  { type: 'number', min: 1, message: 'Quantity must be at least 1' },
                ]}
              >
                <InputNumber
                  placeholder="Enter quantity"
                  size="large"
                  style={{ width: '100%' }}
                  min={1}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item
                label="Action"
                name="action"
                rules={[{ required: true, message: 'Please select action' }]}
              >
                <Radio.Group size="large">
                  <Radio.Button value="buy">Buy</Radio.Button>
                  <Radio.Button value="sell">Sell</Radio.Button>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label=" ">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  size="large"
                  style={{ width: '100%' }}
                >
                  Execute
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* Leaderboard */}
      <Card 
        title={`Top 10 Leaderboard (${userProfits.length} users)`}
        className="shadow-sm"
      >
        <Table
          columns={leaderboardColumns}
          dataSource={userProfits}
          rowKey="userId"
          pagination={false}
          locale={{
            emptyText: 'No users with holdings yet. Start trading to see the leaderboard!',
          }}
        />
      </Card>
    </div>
  );
}

export default Leaderboard;
