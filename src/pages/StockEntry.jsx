import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  InputNumber, 
  Button, 
  Table, 
  Typography, 
  Space,
  message,
  Tag 
} from 'antd';
import { PlusOutlined, StockOutlined } from '@ant-design/icons';
import { useApp } from '../context/AppContext';

const { Title, Text } = Typography;

function StockEntry() {
  const { state, addStock } = useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const { stockName, stockPrice } = values;
      
      // Validate inputs
      if (!stockName || !stockPrice || stockPrice <= 0) {
        message.error('Please provide valid stock name and price');
        return;
      }

      // Check if stock name already exists
      const existingStock = state.stocks.find(
        stock => stock.name.toLowerCase() === stockName.toLowerCase()
      );
      
      if (existingStock) {
        message.error('Stock with this name already exists');
        return;
      }

      addStock(stockName, stockPrice);
      form.resetFields();
      message.success('Stock added successfully!');
    } catch (error) {
      message.error('Failed to add stock');
      console.error('Error adding stock:', error);
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Stock ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id) => <Tag color="blue">#{id}</Tag>,
    },
    {
      title: 'Stock Name',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <Space>
          <StockOutlined />
          <Text strong>{name}</Text>
        </Space>
      ),
    },
    {
      title: 'Current Price',
      dataIndex: 'price',
      key: 'price',
      width: 150,
      render: (price) => (
        <Text strong className="text-green-600">
          ${price.toFixed(2)}
        </Text>
      ),
      sorter: (a, b) => a.price - b.price,
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (date) => new Date(date).toLocaleString(),
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <Title level={2}>Stock Data Entry</Title>
        <Text type="secondary">
          Add new stocks to the system. Stock prices will update automatically every second.
        </Text>
      </div>

      <Card title="Add New Stock" className="shadow-sm">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="max-w-md"
        >
          <Form.Item
            label="Stock Name"
            name="stockName"
            rules={[
              { required: true, message: 'Please enter stock name' },
              { min: 2, message: 'Stock name must be at least 2 characters' },
              { max: 50, message: 'Stock name must be less than 50 characters' },
            ]}
          >
            <Input 
              placeholder="Enter stock name (e.g., Apple Inc.)"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="Stock Price ($)"
            name="stockPrice"
            rules={[
              { required: true, message: 'Please enter stock price' },
              { type: 'number', min: 0.01, message: 'Price must be greater than 0' },
            ]}
          >
            <InputNumber
              placeholder="Enter initial price"
              size="large"
              style={{ width: '100%' }}
              precision={2}
              min={0.01}
              step={0.01}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<PlusOutlined />}
              size="large"
            >
              Add Stock
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card 
        title={`Stock List (${state.stocks.length} stocks)`}
        className="shadow-sm"
      >
        <Table
          columns={columns}
          dataSource={state.stocks}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} stocks`,
          }}
          scroll={{ x: 800 }}
          locale={{
            emptyText: 'No stocks added yet. Add your first stock above!',
          }}
        />
      </Card>
    </div>
  );
}

export default StockEntry;
