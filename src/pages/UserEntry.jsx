import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Table, 
  Typography, 
  Space,
  message,
  Tag,
  Avatar 
} from 'antd';
import { PlusOutlined, UserOutlined } from '@ant-design/icons';
import { useApp } from '../context/AppContext';

const { Title, Text } = Typography;

function UserEntry() {
  const { state, addUser } = useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const { userName } = values;
      
      // Validate input
      if (!userName || userName.trim().length === 0) {
        message.error('Please provide a valid user name');
        return;
      }

      // Check if user name already exists
      const existingUser = state.users.find(
        user => user.name.toLowerCase() === userName.toLowerCase()
      );
      
      if (existingUser) {
        message.error('User with this name already exists');
        return;
      }

      addUser(userName.trim());
      form.resetFields();
      message.success('User added successfully!');
    } catch (error) {
      message.error('Failed to add user');
      console.error('Error adding user:', error);
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'User ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id) => <Tag color="green">#{id}</Tag>,
    },
    {
      title: 'User Name',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <Space>
          <Avatar 
            icon={<UserOutlined />} 
            style={{ backgroundColor: '#1890ff' }}
          />
          <Text strong>{name}</Text>
        </Space>
      ),
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 200,
      render: (date) => new Date(date).toLocaleString(),
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <Title level={2}>User Data Entry</Title>
        <Text type="secondary">
          Add new users to the system. Users can then buy and sell stocks to compete on the leaderboard.
        </Text>
      </div>

      <Card title="Add New User" className="shadow-sm">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="max-w-md"
        >
          <Form.Item
            label="User Name"
            name="userName"
            rules={[
              { required: true, message: 'Please enter user name' },
              { min: 2, message: 'User name must be at least 2 characters' },
              { max: 30, message: 'User name must be less than 30 characters' },
              { 
                pattern: /^[a-zA-Z\s]+$/, 
                message: 'User name can only contain letters and spaces' 
              },
            ]}
          >
            <Input 
              placeholder="Enter user name (e.g., John Doe)"
              size="large"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<PlusOutlined />}
              size="large"
            >
              Add User
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card 
        title={`User List (${state.users.length} users)`}
        className="shadow-sm"
      >
        <Table
          columns={columns}
          dataSource={state.users}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} users`,
          }}
          scroll={{ x: 600 }}
          locale={{
            emptyText: 'No users added yet. Add your first user above!',
          }}
        />
      </Card>
    </div>
  );
}

export default UserEntry;
