/**
 * Stock Price Updater Utility
 * Provides functions for realistic stock price simulation
 */

/**
 * Generate a random price variance between -2% to +2%
 * @returns {number} Variance factor (e.g., 0.02 for +2%, -0.015 for -1.5%)
 */
export function generatePriceVariance() {
  // Generate random number between -0.02 and +0.02 (-2% to +2%)
  return (Math.random() - 0.5) * 0.04;
}

/**
 * Update a single stock price with realistic variance
 * @param {number} currentPrice - Current stock price
 * @param {number} minPrice - Minimum allowed price (default: 0.01)
 * @returns {number} New price rounded to 2 decimal places
 */
export function updateStockPrice(currentPrice, minPrice = 0.01) {
  const variance = generatePriceVariance();
  const newPrice = currentPrice * (1 + variance);
  
  // Ensure price doesn't go below minimum
  const finalPrice = Math.max(minPrice, newPrice);
  
  // Round to 2 decimal places
  return Math.round(finalPrice * 100) / 100;
}

/**
 * Update multiple stock prices
 * @param {Array} stocks - Array of stock objects with price property
 * @returns {Array} Updated stocks array
 */
export function updateAllStockPrices(stocks) {
  return stocks.map(stock => ({
    ...stock,
    price: updateStockPrice(stock.price),
    lastUpdated: new Date().toISOString(),
  }));
}

/**
 * Calculate percentage change between two prices
 * @param {number} oldPrice - Previous price
 * @param {number} newPrice - Current price
 * @returns {number} Percentage change (e.g., 0.05 for +5%)
 */
export function calculatePriceChange(oldPrice, newPrice) {
  if (oldPrice === 0) return 0;
  return (newPrice - oldPrice) / oldPrice;
}

/**
 * Format price change for display
 * @param {number} change - Price change as decimal (e.g., 0.05 for 5%)
 * @returns {string} Formatted string (e.g., "+5.00%")
 */
export function formatPriceChange(change) {
  const percentage = change * 100;
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(2)}%`;
}

/**
 * Simulate market volatility based on time of day
 * Higher volatility during market hours (9 AM - 4 PM EST)
 * @returns {number} Volatility multiplier (1.0 = normal, 1.5 = high volatility)
 */
export function getMarketVolatility() {
  const now = new Date();
  const hour = now.getHours();
  
  // Market hours: 9 AM - 4 PM (EST)
  // Higher volatility during market hours
  if (hour >= 9 && hour <= 16) {
    return 1.5; // 50% more volatile during market hours
  } else {
    return 0.5; // 50% less volatile after hours
  }
}

/**
 * Advanced stock price update with market volatility
 * @param {number} currentPrice - Current stock price
 * @param {number} minPrice - Minimum allowed price
 * @returns {number} New price with market volatility considered
 */
export function updateStockPriceWithVolatility(currentPrice, minPrice = 0.01) {
  const baseVariance = generatePriceVariance();
  const volatilityMultiplier = getMarketVolatility();
  const adjustedVariance = baseVariance * volatilityMultiplier;
  
  const newPrice = currentPrice * (1 + adjustedVariance);
  const finalPrice = Math.max(minPrice, newPrice);
  
  return Math.round(finalPrice * 100) / 100;
}

/**
 * Generate realistic stock price history for testing
 * @param {number} initialPrice - Starting price
 * @param {number} days - Number of days to simulate
 * @returns {Array} Array of price history objects
 */
export function generatePriceHistory(initialPrice, days = 30) {
  const history = [];
  let currentPrice = initialPrice;
  
  for (let i = 0; i < days; i++) {
    const date = new Date();
    date.setDate(date.getDate() - (days - i));
    
    currentPrice = updateStockPrice(currentPrice);
    
    history.push({
      date: date.toISOString().split('T')[0],
      price: currentPrice,
      change: i > 0 ? calculatePriceChange(history[i-1].price, currentPrice) : 0,
    });
  }
  
  return history;
}

/**
 * Stock price update configuration
 */
export const PRICE_UPDATE_CONFIG = {
  UPDATE_INTERVAL: 1000, // 1 second
  MIN_PRICE: 0.01,
  MAX_VARIANCE: 0.02, // ±2%
  VOLATILITY_ENABLED: true,
};

/**
 * Validate stock price
 * @param {number} price - Price to validate
 * @returns {boolean} True if valid
 */
export function isValidPrice(price) {
  return typeof price === 'number' && 
         price > 0 && 
         price < Number.MAX_SAFE_INTEGER &&
         !isNaN(price);
}

/**
 * Format currency for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency symbol (default: '$')
 * @returns {string} Formatted currency string
 */
export function formatCurrency(amount, currency = '$') {
  if (!isValidPrice(amount)) return `${currency}0.00`;
  return `${currency}${amount.toFixed(2)}`;
}
