import React from 'react';
import { Layout as AntLayout, Menu, Typography } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  DashboardOutlined, 
  StockOutlined, 
  UserOutlined,
  TrophyOutlined 
} from '@ant-design/icons';

const { Header, Content } = AntLayout;
const { Title } = Typography;

function Layout({ children }) {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/leaderboard',
      icon: <TrophyOutlined />,
      label: 'Leaderboard',
    },
    {
      key: '/stocks',
      icon: <StockOutlined />,
      label: 'Stock Entry',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: 'User Entry',
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  return (
    <AntLayout className="min-h-screen w-full">
      <Header className="bg-white shadow-sm border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-5">
            <DashboardOutlined className="text-2xl" style={{ color: '#FFF', paddingTop: '7px', marginRight: "10px" }} />
            <Title level={3} className="m-0 text-white" style={{ color: '#FFF' }}>
              Stock Holding Leaderboard
            </Title>
          </div>
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            className="border-none bg-transparent"
          />
        </div>
      </Header>
      <Content className="p-6 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          {children}
        </div>
      </Content>
    </AntLayout>
  );
}

export default Layout;
