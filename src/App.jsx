import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { AppProvider } from './context/AppContext';
import Layout from './components/Layout';
import StockEntry from './pages/StockEntry';
import UserEntry from './pages/UserEntry';
import Leaderboard from './pages/Leaderboard';
import "./App.css";
import "./index.css";

function App() {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1890ff',
        },
      }}
    >
      <AppProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Leaderboard />} />
              <Route path="/stocks" element={<StockEntry />} />
              <Route path="/users" element={<UserEntry />} />
              <Route path="/leaderboard" element={<Leaderboard />} />
            </Routes>
          </Layout>
        </Router>
      </AppProvider>
    </ConfigProvider>
  );
}

export default App;
