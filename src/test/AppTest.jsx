import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import App from '../App';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('Stock Holding Leaderboard App', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockReturnValue(null);
    localStorageMock.setItem.mockClear();
  });

  test('renders main navigation', () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    expect(screen.getByText('Stock Holding Leaderboard')).toBeInTheDocument();
    expect(screen.getByText('Leaderboard')).toBeInTheDocument();
    expect(screen.getByText('Stock Entry')).toBeInTheDocument();
    expect(screen.getByText('User Entry')).toBeInTheDocument();
  });

  test('can add a new stock', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // Navigate to stocks page
    fireEvent.click(screen.getByText('Stock Entry'));

    // Fill in stock form
    const stockNameInput = screen.getByPlaceholderText('Enter stock name (e.g., Apple Inc.)');
    const stockPriceInput = screen.getByPlaceholderText('Enter initial price');
    const addButton = screen.getByText('Add Stock');

    fireEvent.change(stockNameInput, { target: { value: 'Apple Inc.' } });
    fireEvent.change(stockPriceInput, { target: { value: '150.00' } });
    fireEvent.click(addButton);

    // Check if stock was added
    await waitFor(() => {
      expect(screen.getByText('Apple Inc.')).toBeInTheDocument();
    });
  });

  test('can add a new user', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // Navigate to users page
    fireEvent.click(screen.getByText('User Entry'));

    // Fill in user form
    const userNameInput = screen.getByPlaceholderText('Enter user name (e.g., John Doe)');
    const addButton = screen.getByText('Add User');

    fireEvent.change(userNameInput, { target: { value: 'John Doe' } });
    fireEvent.click(addButton);

    // Check if user was added
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });

  test('displays leaderboard correctly', () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // Should be on leaderboard by default
    expect(screen.getByText('Stock Holding Leaderboard')).toBeInTheDocument();
    expect(screen.getByText('Total Users')).toBeInTheDocument();
    expect(screen.getByText('Total Stocks')).toBeInTheDocument();
    expect(screen.getByText('Total Transactions')).toBeInTheDocument();
    expect(screen.getByText('Top Profit')).toBeInTheDocument();
  });

  test('validates stock input correctly', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // Navigate to stocks page
    fireEvent.click(screen.getByText('Stock Entry'));

    // Try to submit empty form
    const addButton = screen.getByText('Add Stock');
    fireEvent.click(addButton);

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText('Please enter stock name')).toBeInTheDocument();
      expect(screen.getByText('Please enter stock price')).toBeInTheDocument();
    });
  });

  test('validates user input correctly', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // Navigate to users page
    fireEvent.click(screen.getByText('User Entry'));

    // Try to submit empty form
    const addButton = screen.getByText('Add User');
    fireEvent.click(addButton);

    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText('Please enter user name')).toBeInTheDocument();
    });
  });
});

// Manual testing instructions
console.log(`
=== MANUAL TESTING INSTRUCTIONS ===

1. Stock Entry Page (/stocks):
   - Add stocks with different names and prices
   - Verify auto-generated IDs
   - Watch real-time price updates (every second)
   - Test validation (empty fields, duplicate names)

2. User Entry Page (/users):
   - Add users with different names
   - Verify auto-generated IDs
   - Test validation (empty fields, duplicate names)

3. Leaderboard Page (/leaderboard):
   - View statistics dashboard
   - See current stock prices updating
   - Execute buy/sell transactions
   - Watch leaderboard update in real-time
   - Test with multiple users and stocks

4. Data Persistence:
   - Refresh the page and verify data persists
   - Close and reopen browser tab

5. Edge Cases:
   - Try selling more stocks than owned
   - Add stocks with very low/high prices
   - Test with many users and transactions

6. Performance:
   - Add 50+ stocks and users
   - Execute 100+ transactions
   - Verify real-time updates still work smoothly
`);
