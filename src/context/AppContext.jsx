import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useLocalStorage } from '../hooks/useLocalStorage';

const AppContext = createContext();

// Action types
const ACTIONS = {
  ADD_STOCK: 'ADD_STOCK',
  UPDATE_STOCK_PRICE: 'UPDATE_STOCK_PRICE',
  ADD_USER: 'ADD_USER',
  ADD_HOLDING: 'ADD_HOLDING',
  UPDATE_ALL_STOCK_PRICES: 'UPDATE_ALL_STOCK_PRICES',
};

// Initial state
const initialState = {
  stocks: [],
  users: [],
  holdings: [], // { id, userId, stockId, quantity, buyPrice, action, timestamp }
};

// Reducer function
function appReducer(state, action) {
  switch (action.type) {
    case ACTIONS.ADD_STOCK:
      return {
        ...state,
        stocks: [...state.stocks, action.payload],
      };

    case ACTIONS.UPDATE_STOCK_PRICE:
      return {
        ...state,
        stocks: state.stocks.map(stock =>
          stock.id === action.payload.id
            ? { ...stock, price: action.payload.price }
            : stock
        ),
      };

    case ACTIONS.UPDATE_ALL_STOCK_PRICES:
      return {
        ...state,
        stocks: state.stocks.map(stock => {
          // Random variance between -2% to +2%
          const variance = (Math.random() - 0.5) * 0.04; // -0.02 to +0.02
          const newPrice = Math.max(0.01, stock.price * (1 + variance));
          return { ...stock, price: Math.round(newPrice * 100) / 100 };
        }),
      };

    case ACTIONS.ADD_USER:
      return {
        ...state,
        users: [...state.users, action.payload],
      };

    case ACTIONS.ADD_HOLDING:
      return {
        ...state,
        holdings: [...state.holdings, action.payload],
      };

    default:
      return state;
  }
}

// Context Provider Component
export function AppProvider({ children }) {
  const [storedStocks, setStoredStocks] = useLocalStorage('stocks', []);
  const [storedUsers, setStoredUsers] = useLocalStorage('users', []);
  const [storedHoldings, setStoredHoldings] = useLocalStorage('holdings', []);

  const [state, dispatch] = useReducer(appReducer, {
    stocks: storedStocks,
    users: storedUsers,
    holdings: storedHoldings,
  });

  // Sync state with localStorage
  useEffect(() => {
    setStoredStocks(state.stocks);
  }, [state.stocks, setStoredStocks]);

  useEffect(() => {
    setStoredUsers(state.users);
  }, [state.users, setStoredUsers]);

  useEffect(() => {
    setStoredHoldings(state.holdings);
  }, [state.holdings, setStoredHoldings]);

  // Real-time price updates every second
  useEffect(() => {
    const interval = setInterval(() => {
      if (state.stocks.length > 0) {
        dispatch({ type: ACTIONS.UPDATE_ALL_STOCK_PRICES });
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [state.stocks.length]);

  // Action creators
  const addStock = (name, price) => {
    const newStock = {
      id: Date.now(),
      name,
      price: parseFloat(price),
      createdAt: new Date().toISOString(),
    };
    dispatch({ type: ACTIONS.ADD_STOCK, payload: newStock });
  };

  const addUser = (name) => {
    const newUser = {
      id: Date.now(),
      name,
      createdAt: new Date().toISOString(),
    };
    dispatch({ type: ACTIONS.ADD_USER, payload: newUser });
  };

  const addHolding = (userId, stockId, quantity, action) => {
    const stock = state.stocks.find(s => s.id === parseInt(stockId));
    if (!stock) return;

    const newHolding = {
      id: Date.now(),
      userId: parseInt(userId),
      stockId: parseInt(stockId),
      quantity: parseInt(quantity),
      buyPrice: stock.price,
      action, // 'buy' or 'sell'
      timestamp: new Date().toISOString(),
    };
    dispatch({ type: ACTIONS.ADD_HOLDING, payload: newHolding });
  };

  // Calculate user profits
  const calculateUserProfits = () => {
    const userProfits = {};

    // Initialize all users with 0 profit
    state.users.forEach(user => {
      userProfits[user.id] = {
        userId: user.id,
        userName: user.name,
        totalProfit: 0,
        holdings: {},
      };
    });

    // Process all holdings
    state.holdings.forEach(holding => {
      const stock = state.stocks.find(s => s.id === holding.stockId);
      if (!stock || !userProfits[holding.userId]) return;

      const stockKey = holding.stockId;
      if (!userProfits[holding.userId].holdings[stockKey]) {
        userProfits[holding.userId].holdings[stockKey] = {
          quantity: 0,
          totalCost: 0,
        };
      }

      const userHolding = userProfits[holding.userId].holdings[stockKey];

      if (holding.action === 'buy') {
        userHolding.quantity += holding.quantity;
        userHolding.totalCost += holding.quantity * holding.buyPrice;
      } else if (holding.action === 'sell') {
        // For selling, we calculate profit immediately
        const avgBuyPrice = userHolding.quantity > 0 ? userHolding.totalCost / userHolding.quantity : holding.buyPrice;
        const sellProfit = holding.quantity * (holding.buyPrice - avgBuyPrice);
        userProfits[holding.userId].totalProfit += sellProfit;

        // Reduce holdings
        const sellRatio = Math.min(1, holding.quantity / userHolding.quantity);
        userHolding.quantity -= holding.quantity;
        userHolding.totalCost -= userHolding.totalCost * sellRatio;
        
        if (userHolding.quantity <= 0) {
          userHolding.quantity = 0;
          userHolding.totalCost = 0;
        }
      }
    });

    // Calculate current profits for remaining holdings
    Object.values(userProfits).forEach(userProfit => {
      Object.entries(userProfit.holdings).forEach(([stockId, holding]) => {
        if (holding.quantity > 0) {
          const stock = state.stocks.find(s => s.id === parseInt(stockId));
          if (stock) {
            const avgBuyPrice = holding.totalCost / holding.quantity;
            const currentProfit = holding.quantity * (stock.price - avgBuyPrice);
            userProfit.totalProfit += currentProfit;
          }
        }
      });
    });

    return Object.values(userProfits)
      .sort((a, b) => b.totalProfit - a.totalProfit)
      .slice(0, 10); // Top 10
  };

  const value = {
    state,
    addStock,
    addUser,
    addHolding,
    calculateUserProfits,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hook to use the context
export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
